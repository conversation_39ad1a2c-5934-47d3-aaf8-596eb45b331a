package com.tfkcolin.cebsscada

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Bluetooth
import androidx.compose.material.icons.filled.Science
import androidx.compose.material.icons.filled.BugReport
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.tfkcolin.cebsscada.bluetooth.BluetoothViewModel
import com.tfkcolin.cebsscada.ui.permissions.BluetoothApp
import com.tfkcolin.cebsscada.ui.testing.TestingScreen
import com.tfkcolin.cebsscada.ui.debug.DebugScreen
import com.tfkcolin.cebsscada.ui.theme.CEBSSCADATheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            CEBSSCADATheme {
                BluetoothTestApp()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BluetoothTestApp() {
    var selectedTab by remember { mutableStateOf(0) }
    val bluetoothViewModel: BluetoothViewModel = viewModel()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("CEBS SCADA - Bluetooth Test") }
            )
        },
        bottomBar = {
            NavigationBar {
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Bluetooth, contentDescription = "Bluetooth") },
                    label = { Text("Bluetooth") },
                    selected = selectedTab == 0,
                    onClick = { selectedTab = 0 }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Science, contentDescription = "Testing") },
                    label = { Text("Testing") },
                    selected = selectedTab == 1,
                    onClick = { selectedTab = 1 }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.BugReport, contentDescription = "Debug") },
                    label = { Text("Debug") },
                    selected = selectedTab == 2,
                    onClick = { selectedTab = 2 }
                )
            }
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            when (selectedTab) {
                0 -> BluetoothApp()
                1 -> TestingScreen(bluetoothViewModel)
                2 -> DebugScreen(bluetoothViewModel)
            }
        }
    }
}