package com.tfkcolin.cebsscada.bluetooth

import java.util.*

object BluetoothConstants {
    // Service Actions
    const val ACTION_CONNECTED = "com.tfkcolin.cebsscada.bluetooth.CONNECTED"
    const val ACTION_DISCONNECTED = "com.tfkcolin.cebsscada.bluetooth.DISCONNECTED"
    const val ACTION_CONNECTION_FAILED = "com.tfkcolin.cebsscada.bluetooth.CONNECTION_FAILED"
    const val ACTION_CONNECTION_LOST = "com.tfkcolin.cebsscada.bluetooth.CONNECTION_LOST"
    const val ACTION_MESSAGE_RECEIVED = "com.tfkcolin.cebsscada.bluetooth.MESSAGE_RECEIVED"
    const val ACTION_MESSAGE_SENT = "com.tfkcolin.cebsscada.bluetooth.MESSAGE_SENT"
    const val ACTION_WRITE_FAILED = "com.tfkcolin.cebsscada.bluetooth.WRITE_FAILED"
    
    // Receiver Actions
    const val ACTION_BLUETOOTH_ENABLED = "com.tfkcolin.cebsscada.bluetooth.BLUETOOTH_ENABLED"
    const val ACTION_BLUETOOTH_DISABLED = "com.tfkcolin.cebsscada.bluetooth.BLUETOOTH_DISABLED"
    const val ACTION_DEVICE_DISCOVERED = "com.tfkcolin.cebsscada.bluetooth.DEVICE_DISCOVERED"
    const val ACTION_DISCOVERY_FINISHED = "com.tfkcolin.cebsscada.bluetooth.DISCOVERY_FINISHED"
    const val ACTION_DEVICE_CONNECTED = "com.tfkcolin.cebsscada.bluetooth.DEVICE_CONNECTED"
    const val ACTION_DEVICE_DISCONNECTED = "com.tfkcolin.cebsscada.bluetooth.DEVICE_DISCONNECTED"
    
    // BLE Actions
    const val ACTION_BLE_DEVICE_DISCOVERED = "com.tfkcolin.cebsscada.bluetooth.BLE_DEVICE_DISCOVERED"
    const val ACTION_BLE_SERVICES_DISCOVERED = "com.tfkcolin.cebsscada.bluetooth.BLE_SERVICES_DISCOVERED"
    const val ACTION_BLE_DATA_AVAILABLE = "com.tfkcolin.cebsscada.bluetooth.BLE_DATA_AVAILABLE"
    
    // Error Codes
    const val ERROR_NONE = 0
    const val ERROR_CONNECTION_TIMEOUT = 1
    const val ERROR_CONNECTION_FAILED = 2
    const val ERROR_CONNECTION_LOST = 3
    const val ERROR_WRITE_FAILED = 4
    const val ERROR_BLUETOOTH_NOT_AVAILABLE = 5
    const val ERROR_BLUETOOTH_NOT_ENABLED = 6
    const val ERROR_DEVICE_NOT_FOUND = 7
    const val ERROR_INVALID_ADDRESS = 8
    const val ERROR_BLE_NOT_SUPPORTED = 9
    const val ERROR_BLE_SERVICES_NOT_FOUND = 10
    
    // Error Messages
    val ERROR_MESSAGES = mapOf(
        ERROR_NONE to "No error",
        ERROR_CONNECTION_TIMEOUT to "Connection timeout",
        ERROR_CONNECTION_FAILED to "Connection failed",
        ERROR_CONNECTION_LOST to "Connection lost",
        ERROR_WRITE_FAILED to "Write failed",
        ERROR_BLUETOOTH_NOT_AVAILABLE to "Bluetooth not available",
        ERROR_BLUETOOTH_NOT_ENABLED to "Bluetooth not enabled",
        ERROR_DEVICE_NOT_FOUND to "Device not found",
        ERROR_INVALID_ADDRESS to "Invalid device address",
        ERROR_BLE_NOT_SUPPORTED to "BLE not supported",
        ERROR_BLE_SERVICES_NOT_FOUND to "BLE services not found"
    )
    
    // Connection Timeout in milliseconds
    const val CONNECTION_TIMEOUT = 10000L // 10 seconds
    
    // BLE UUIDs (common ones)
    val UUID_BLE_GENERIC_ACCESS = UUID.fromString("00001800-0000-1000-8000-00805F9B34FB")
    val UUID_BLE_DEVICE_NAME = UUID.fromString("00002A00-0000-1000-8000-00805F9B34FB")
    val UUID_BLE_GENERIC_ATTRIBUTE = UUID.fromString("00001801-0000-1000-8000-00805F9B34FB")
    val UUID_BLE_SERVICE_CHANGED = UUID.fromString("00002A05-0000-1000-8000-00805F9B34FB")
}
