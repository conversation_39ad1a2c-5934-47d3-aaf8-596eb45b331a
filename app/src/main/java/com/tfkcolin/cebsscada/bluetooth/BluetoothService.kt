package com.tfkcolin.cebsscada.bluetooth

import android.Manifest
import android.app.Service
import android.bluetooth.*
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Binder
import android.os.IBinder
import android.util.Log
import androidx.annotation.RequiresPermission
import java.io.IOException
import java.util.*
import java.util.concurrent.BlockingQueue
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicBoolean

class BluetoothService : Service() {
    private val binder = LocalBinder()
    private var bluetoothAdapter: BluetoothAdapter? = null
    private var socket: BluetoothSocket? = null
    private var device: BluetoothDevice? = null
    private var connectedThread: ConnectedThread? = null
    private var bleGatt: BluetoothGatt? = null
    private var connectionState = STATE_NONE
    private val isConnecting = AtomicBoolean(false)
    private val messageQueue: BlockingQueue<ByteArray> = LinkedBlockingQueue()
    private var bleCallback: BluetoothGattCallback? = null

    companion object {
        const val TAG = "BluetoothService"
        const val STATE_NONE = 0
        const val STATE_CONNECTING = 1
        const val STATE_CONNECTED = 2
        const val STATE_BLE_CONNECTED = 3
        val UUID_SPP = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
    }

    inner class LocalBinder : Binder() {
        fun getService(): BluetoothService = this@BluetoothService
    }

    override fun onBind(intent: Intent): IBinder = binder

    override fun onCreate() {
        super.onCreate()
        initialize()
    }

    fun initialize(): Boolean {
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        if (bluetoothAdapter == null) {
            broadcastUpdate(BluetoothConstants.ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_BLUETOOTH_NOT_AVAILABLE, "")
            return false
        }
        if (!bluetoothAdapter!!.isEnabled) {
            broadcastUpdate(BluetoothConstants.ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_BLUETOOTH_NOT_ENABLED, "")
            return false
        }
        return true
    }

    fun connect(device: BluetoothDevice, useBLE: Boolean = false): Boolean {
        if (connectionState == STATE_CONNECTED || connectionState == STATE_BLE_CONNECTED) {
            disconnect()
        }

        if (isConnecting.get()) {
            closeSocket()
            closeBLEConnection()
        }

        isConnecting.set(true)
        this.device = device
        connectionState = STATE_CONNECTING

        if (useBLE) {
            connectBLE(device)
        } else {
            connectSPP(device)
        }

        return true
    }

    private fun connectSPP(device: BluetoothDevice) {
        Thread {
            try {
                val tmpSocket = device.createRfcommSocketToServiceRecord(UUID_SPP)
                bluetoothAdapter?.cancelDiscovery()
                tmpSocket.connect()
                
                socket = tmpSocket
                connectedThread = ConnectedThread(socket)
                connectedThread?.start()
                connectionState = STATE_CONNECTED
                isConnecting.set(false)
                broadcastUpdate(BluetoothConstants.ACTION_CONNECTED, BluetoothConstants.ERROR_NONE, device.address)
            } catch (e: IOException) {
                connectionState = STATE_NONE
                isConnecting.set(false)
                broadcastUpdate(BluetoothConstants.ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_CONNECTION_FAILED, "")
                closeSocket()
            }
        }.start()

        // Timeout thread
        Thread {
            Thread.sleep(BluetoothConstants.CONNECTION_TIMEOUT)
            if (isConnecting.get()) {
                isConnecting.set(false)
                closeSocket()
                connectionState = STATE_NONE
                broadcastUpdate(BluetoothConstants.ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_CONNECTION_TIMEOUT, "")
            }
        }.start()
    }

    private fun connectBLE(device: BluetoothDevice) {
        if (!packageManager.hasSystemFeature(PackageManager.FEATURE_BLUETOOTH_LE)) {
            broadcastUpdate(BluetoothConstants.ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_BLE_NOT_SUPPORTED, "")
            isConnecting.set(false)
            return
        }

        bleCallback = object : BluetoothGattCallback() {
            override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
                if (newState == BluetoothProfile.STATE_CONNECTED) {
                    connectionState = STATE_BLE_CONNECTED
                    isConnecting.set(false)
                    broadcastUpdate(BluetoothConstants.ACTION_CONNECTED, BluetoothConstants.ERROR_NONE, device.address)
                    gatt.discoverServices()
                } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                    connectionState = STATE_NONE
                    broadcastUpdate(BluetoothConstants.ACTION_DISCONNECTED)
                    closeBLEConnection()
                }
            }

            override fun onServicesDiscovered(gatt: BluetoothGatt, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    broadcastUpdate(BluetoothConstants.ACTION_BLE_SERVICES_DISCOVERED)
                } else {
                    broadcastUpdate(BluetoothConstants.ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_BLE_SERVICES_NOT_FOUND, "")
                }
            }

            override fun onCharacteristicChanged(
                gatt: BluetoothGatt,
                characteristic: BluetoothGattCharacteristic,
                value: ByteArray
            ) {
                broadcastUpdate(BluetoothConstants.ACTION_BLE_DATA_AVAILABLE, BluetoothConstants.ERROR_NONE, value)
            }
        }

        bleGatt = device.connectGatt(this, false, bleCallback)
        
        // Timeout thread
        Thread {
            Thread.sleep(BluetoothConstants.CONNECTION_TIMEOUT)
            if (isConnecting.get()) {
                isConnecting.set(false)
                closeBLEConnection()
                connectionState = STATE_NONE
                broadcastUpdate(BluetoothConstants.ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_CONNECTION_TIMEOUT, "")
            }
        }.start()
    }

    fun write(bytes: ByteArray) {
        when (connectionState) {
            STATE_CONNECTED -> {
                messageQueue.add(bytes)
                connectedThread?.notifyNewMessage()
            }
            STATE_BLE_CONNECTED -> {
                // For BLE, we need to write to a specific characteristic
                // This is a simplified implementation - in practice you'd identify the correct characteristic
                bleGatt?.services?.forEach { service ->
                    service.characteristics.forEach { characteristic ->
                        if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE != 0) {
                            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                                bleGatt?.writeCharacteristic(characteristic, bytes, BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT)
                            } else {
                                @Suppress("DEPRECATION")
                                characteristic.value = bytes
                                @Suppress("DEPRECATION")
                                bleGatt?.writeCharacteristic(characteristic)
                            }
                        }
                    }
                }
            }
        }
    }

    fun disconnect() {
        when (connectionState) {
            STATE_CONNECTED -> {
                connectedThread?.cancel()
                closeSocket()
            }
            STATE_BLE_CONNECTED -> {
                closeBLEConnection()
            }
        }
        connectionState = STATE_NONE
        broadcastUpdate(BluetoothConstants.ACTION_DISCONNECTED)
    }

    private fun closeSocket() {
        try {
            socket?.close()
        } catch (e: IOException) {
            Log.e(TAG, "Socket close error", e)
        }
    }

    @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
    private fun closeBLEConnection() {
        bleGatt?.disconnect()
        bleGatt?.close()
        bleGatt = null
    }

    private inner class ConnectedThread(private val socket: BluetoothSocket?) {
        private val inputStream = socket?.inputStream
        private val outputStream = socket?.outputStream
        private val buffer = ByteArray(1024)
        private val shouldContinue = AtomicBoolean(true)
        private val hasMessage = Object() // For wait/notify
        private val readThread = Thread {
            while (shouldContinue.get()) {
                try {
                    val bytes = inputStream?.read(buffer) ?: break
                    val message = String(buffer, 0, bytes)
                    broadcastUpdate(BluetoothConstants.ACTION_MESSAGE_RECEIVED, BluetoothConstants.ERROR_NONE, message)
                } catch (e: IOException) {
                    if (shouldContinue.get()) {
                        connectionState = STATE_NONE
                        broadcastUpdate(BluetoothConstants.ACTION_CONNECTION_LOST, BluetoothConstants.ERROR_CONNECTION_LOST)
                    }
                    break
                }
            }
        }
        private val writeThread = Thread {
            while (shouldContinue.get()) {
                try {
                    val bytes = if (messageQueue.isEmpty()) {
                        synchronized(hasMessage) {
                            (hasMessage as Object).wait()
                        }
                        messageQueue.peek()
                    } else {
                        messageQueue.peek()
                    }

                    if (bytes != null) {
                        outputStream?.write(bytes)
                        messageQueue.remove()
                        broadcastUpdate(BluetoothConstants.ACTION_MESSAGE_SENT)
                    }
                } catch (e: InterruptedException) {
                    if (shouldContinue.get()) {
                        Log.e(TAG, "Write thread interrupted", e)
                    }
                } catch (e: IOException) {
                    broadcastUpdate(BluetoothConstants.ACTION_WRITE_FAILED, BluetoothConstants.ERROR_WRITE_FAILED)
                }
            }
        }

        fun start() {
            readThread.start()
            writeThread.start()
        }

        fun notifyNewMessage() {
            synchronized(hasMessage) {
                (hasMessage as Object).notify()
            }
        }

        fun cancel() {
            shouldContinue.set(false)
            readThread.interrupt()
            writeThread.interrupt()
            try {
                socket?.close()
            } catch (e: IOException) {
                Log.e(TAG, "Thread cancel error", e)
            }
        }
    }

    private fun broadcastUpdate(action: String, errorCode: Int = BluetoothConstants.ERROR_NONE, data: Any = "") {
        val intent = Intent(action).apply {
            putExtra("ERROR_CODE", errorCode)
            putExtra("ERROR_MESSAGE", BluetoothConstants.ERROR_MESSAGES[errorCode] ?: "Unknown error")
            when (data) {
                is String -> putExtra("MESSAGE", data)
                is ByteArray -> putExtra("DATA", data)
                is BluetoothDevice -> putExtra("DEVICE", data)
            }
            putExtra("DEVICE_ADDRESS", device?.address)
        }
        sendBroadcast(intent)
    }
}
