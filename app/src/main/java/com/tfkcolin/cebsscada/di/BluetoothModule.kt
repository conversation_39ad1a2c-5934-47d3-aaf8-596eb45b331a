package com.tfkcolin.cebsscada.di

import android.content.Context
import com.tfkcolin.cebsscada.bluetooth.BLEScanner
import com.tfkcolin.cebsscada.bluetooth.BluetoothService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object BluetoothModule {

    @Provides
    @Singleton
    fun provideBLEScanner(@ApplicationContext context: Context): BLEScanner {
        return BLEScanner(context)
    }

    @Provides
    @Singleton
    fun provideBluetoothService(): BluetoothService {
        return BluetoothService()
    }
}
