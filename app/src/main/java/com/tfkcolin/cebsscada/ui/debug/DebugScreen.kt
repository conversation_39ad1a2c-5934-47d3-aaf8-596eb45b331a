package com.tfkcolin.cebsscada.ui.debug

import android.bluetooth.BluetoothAdapter
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.bluetooth.BluetoothViewModel
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DebugScreen(
    bluetoothViewModel: BluetoothViewModel
) {
    val context = LocalContext.current
    var debugLogs by remember { mutableStateOf<List<DebugLog>>(emptyList()) }
    var showSystemInfo by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "Debug & Diagnostics",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Debug controls
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Button(
                onClick = {
                    debugLogs = debugLogs + DebugLog(
                        level = LogLevel.INFO,
                        message = "Manual debug log entry",
                        timestamp = System.currentTimeMillis()
                    )
                }
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Add Log")
            }
            
            Button(
                onClick = { debugLogs = emptyList() }
            ) {
                Icon(Icons.Default.Clear, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Clear")
            }
            
            Button(
                onClick = { showSystemInfo = !showSystemInfo }
            ) {
                Icon(Icons.Default.Info, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("System Info")
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        if (showSystemInfo) {
            SystemInfoCard()
            Spacer(modifier = Modifier.height(16.dp))
        }
        
        // Debug logs
        Card(
            modifier = Modifier.fillMaxSize(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Debug Logs",
                    style = MaterialTheme.typography.titleMedium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                if (debugLogs.isEmpty()) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "No debug logs yet",
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                } else {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        reverseLayout = true
                    ) {
                        items(debugLogs.reversed()) { log ->
                            DebugLogItem(log = log)
                            Spacer(modifier = Modifier.height(4.dp))
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SystemInfoCard() {
    val context = LocalContext.current
    val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "System Information",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            SystemInfoItem("Android Version", android.os.Build.VERSION.RELEASE)
            SystemInfoItem("API Level", android.os.Build.VERSION.SDK_INT.toString())
            SystemInfoItem("Device Model", "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}")
            SystemInfoItem("Bluetooth Available", (bluetoothAdapter != null).toString())
            SystemInfoItem("Bluetooth Enabled", (bluetoothAdapter?.isEnabled == true).toString())
            SystemInfoItem("Bluetooth Address", bluetoothAdapter?.address ?: "N/A")
            SystemInfoItem("Bluetooth Name", bluetoothAdapter?.name ?: "N/A")
            SystemInfoItem("BLE Support", context.packageManager.hasSystemFeature("android.hardware.bluetooth_le").toString())
        }
    }
}

@Composable
fun SystemInfoItem(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary
        )
    }
}

@Composable
fun DebugLogItem(log: DebugLog) {
    val timeFormat = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.Top
    ) {
        Icon(
            imageVector = when (log.level) {
                LogLevel.ERROR -> Icons.Default.Error
                LogLevel.WARNING -> Icons.Default.Warning
                LogLevel.INFO -> Icons.Default.Info
                LogLevel.DEBUG -> Icons.Default.BugReport
            },
            contentDescription = log.level.name,
            tint = when (log.level) {
                LogLevel.ERROR -> MaterialTheme.colorScheme.error
                LogLevel.WARNING -> MaterialTheme.colorScheme.tertiary
                LogLevel.INFO -> MaterialTheme.colorScheme.primary
                LogLevel.DEBUG -> MaterialTheme.colorScheme.secondary
            },
            modifier = Modifier.size(16.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = log.message,
                style = MaterialTheme.typography.bodySmall
            )
            Text(
                text = timeFormat.format(Date(log.timestamp)),
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
    }
}

data class DebugLog(
    val level: LogLevel,
    val message: String,
    val timestamp: Long
)

enum class LogLevel {
    ERROR, WARNING, INFO, DEBUG
}
