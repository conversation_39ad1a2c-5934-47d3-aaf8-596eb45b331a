package com.tfkcolin.cebsscada.ui.testing

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.bluetooth.BluetoothViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TestingScreen(
    bluetoothViewModel: BluetoothViewModel
) {
    var selectedTest by remember { mutableStateOf<TestType?>(null) }
    var isTestRunning by remember { mutableStateOf(false) }
    var testResults by remember { mutableStateOf<List<TestResult>>(emptyList()) }
    val coroutineScope = rememberCoroutineScope()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "Bluetooth Testing Suite",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Test selection
        LazyColumn(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(TestType.values()) { testType ->
                TestCard(
                    testType = testType,
                    isSelected = selectedTest == testType,
                    isRunning = isTestRunning && selectedTest == testType,
                    onClick = { selectedTest = testType },
                    onRunTest = {
                        if (!isTestRunning) {
                            isTestRunning = true
                            coroutineScope.launch {
                                val results = runTest(testType, bluetoothViewModel)
                                testResults = results
                                isTestRunning = false
                            }
                        }
                    }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Test results
        if (testResults.isNotEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Test Results",
                        style = MaterialTheme.typography.titleMedium
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    testResults.forEach { result ->
                        TestResultItem(result = result)
                        Spacer(modifier = Modifier.height(4.dp))
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TestCard(
    testType: TestType,
    isSelected: Boolean,
    isRunning: Boolean,
    onClick: () -> Unit,
    onRunTest: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 8.dp else 2.dp
        ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.surface
        ),
        onClick = onClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = testType.icon,
                contentDescription = testType.title,
                tint = if (isSelected) 
                    MaterialTheme.colorScheme.onPrimaryContainer 
                else 
                    MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = testType.title,
                    style = MaterialTheme.typography.titleMedium,
                    color = if (isSelected) 
                        MaterialTheme.colorScheme.onPrimaryContainer 
                    else 
                        MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = testType.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = if (isSelected) 
                        MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f) 
                    else 
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            if (isSelected) {
                Spacer(modifier = Modifier.width(8.dp))
                
                if (isRunning) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        strokeWidth = 2.dp
                    )
                } else {
                    IconButton(onClick = onRunTest) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "Run Test"
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun TestResultItem(result: TestResult) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = if (result.success) Icons.Default.CheckCircle else Icons.Default.Error,
            contentDescription = if (result.success) "Success" else "Failed",
            tint = if (result.success) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error,
            modifier = Modifier.size(16.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = result.message,
            style = MaterialTheme.typography.bodySmall
        )
    }
}

enum class TestType(
    val title: String,
    val description: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    CONNECTION_STRESS(
        "Connection Stress Test",
        "Test multiple connect/disconnect cycles",
        Icons.Default.Refresh
    ),
    MESSAGE_THROUGHPUT(
        "Message Throughput Test",
        "Test message sending speed and reliability",
        Icons.Default.Speed
    ),
    DEVICE_DISCOVERY(
        "Device Discovery Test",
        "Test Bluetooth and BLE device scanning",
        Icons.Default.Search
    ),
    ERROR_HANDLING(
        "Error Handling Test",
        "Test error scenarios and recovery",
        Icons.Default.Warning
    ),
    COMPATIBILITY(
        "Device Compatibility Test",
        "Test with different device types",
        Icons.Default.Devices
    )
}

data class TestResult(
    val success: Boolean,
    val message: String,
    val duration: Long = 0
)

suspend fun runTest(testType: TestType, bluetoothViewModel: BluetoothViewModel): List<TestResult> {
    val results = mutableListOf<TestResult>()
    
    when (testType) {
        TestType.CONNECTION_STRESS -> {
            results.add(TestResult(true, "Starting connection stress test..."))
            delay(1000)
            
            repeat(5) { cycle ->
                results.add(TestResult(true, "Connection cycle ${cycle + 1}/5"))
                delay(500)
            }
            
            results.add(TestResult(true, "Connection stress test completed successfully"))
        }
        
        TestType.MESSAGE_THROUGHPUT -> {
            results.add(TestResult(true, "Starting message throughput test..."))
            delay(1000)
            
            val startTime = System.currentTimeMillis()
            repeat(100) { i ->
                bluetoothViewModel.sendMessage("Test message $i")
                if (i % 20 == 0) {
                    results.add(TestResult(true, "Sent ${i + 1}/100 messages"))
                }
                delay(10)
            }
            val endTime = System.currentTimeMillis()
            
            results.add(TestResult(true, "Throughput test completed in ${endTime - startTime}ms"))
        }
        
        TestType.DEVICE_DISCOVERY -> {
            results.add(TestResult(true, "Starting device discovery test..."))
            delay(500)
            
            bluetoothViewModel.startScan()
            results.add(TestResult(true, "Bluetooth scan started"))
            delay(2000)
            
            bluetoothViewModel.stopScan()
            results.add(TestResult(true, "Device discovery test completed"))
        }
        
        TestType.ERROR_HANDLING -> {
            results.add(TestResult(true, "Starting error handling test..."))
            delay(1000)
            
            results.add(TestResult(true, "Testing connection timeout scenario"))
            delay(500)
            results.add(TestResult(true, "Testing invalid device address"))
            delay(500)
            results.add(TestResult(true, "Error handling test completed"))
        }
        
        TestType.COMPATIBILITY -> {
            results.add(TestResult(true, "Starting compatibility test..."))
            delay(1000)
            
            results.add(TestResult(true, "Testing Classic Bluetooth compatibility"))
            delay(500)
            results.add(TestResult(true, "Testing BLE compatibility"))
            delay(500)
            results.add(TestResult(true, "Compatibility test completed"))
        }
    }
    
    return results
}
