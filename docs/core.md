

I'll implement the extensions I highlighted, including timeout handling, message queuing, error codes, and BLE support. Here's the enhanced implementation:

### 1. Updated BluetoothConstants.kt (with Error Codes and Extensions)
```kotlin
object BluetoothConstants {
    // Service Actions
    const val ACTION_CONNECTED = "com.example.bluetooth.CONNECTED"
    const val ACTION_DISCONNECTED = "com.example.bluetooth.DISCONNECTED"
    const val ACTION_CONNECTION_FAILED = "com.example.bluetooth.CONNECTION_FAILED"
    const val ACTION_CONNECTION_LOST = "com.example.bluetooth.CONNECTION_LOST"
    const val ACTION_MESSAGE_RECEIVED = "com.example.bluetooth.MESSAGE_RECEIVED"
    const val ACTION_MESSAGE_SENT = "com.example.bluetooth.MESSAGE_SENT"
    const val ACTION_WRITE_FAILED = "com.example.bluetooth.WRITE_FAILED"
    
    // Receiver Actions
    const val ACTION_BLUETOOTH_ENABLED = "com.example.bluetooth.BLUETOOTH_ENABLED"
    const val ACTION_BLUETOOTH_DISABLED = "com.example.bluetooth.BLUETOOTH_DISABLED"
    const val ACTION_DEVICE_DISCOVERED = "com.example.bluetooth.DEVICE_DISCOVERED"
    const val ACTION_DISCOVERY_FINISHED = "com.example.bluetooth.DISCOVERY_FINISHED"
    const val ACTION_DEVICE_CONNECTED = "com.example.bluetooth.DEVICE_CONNECTED"
    const val ACTION_DEVICE_DISCONNECTED = "com.example.bluetooth.DEVICE_DISCONNECTED"
    
    // BLE Actions
    const val ACTION_BLE_DEVICE_DISCOVERED = "com.example.bluetooth.BLE_DEVICE_DISCOVERED"
    const val ACTION_BLE_SERVICES_DISCOVERED = "com.example.bluetooth.BLE_SERVICES_DISCOVERED"
    const val ACTION_BLE_DATA_AVAILABLE = "com.example.bluetooth.BLE_DATA_AVAILABLE"
    
    // Error Codes
    const val ERROR_NONE = 0
    const val ERROR_CONNECTION_TIMEOUT = 1
    const val ERROR_CONNECTION_FAILED = 2
    const val ERROR_CONNECTION_LOST = 3
    const val ERROR_WRITE_FAILED = 4
    const val ERROR_BLUETOOTH_NOT_AVAILABLE = 5
    const val ERROR_BLUETOOTH_NOT_ENABLED = 6
    const val ERROR_DEVICE_NOT_FOUND = 7
    const val ERROR_INVALID_ADDRESS = 8
    const val ERROR_BLE_NOT_SUPPORTED = 9
    const val ERROR_BLE_SERVICES_NOT_FOUND = 10
    
    // Error Messages
    val ERROR_MESSAGES = mapOf(
        ERROR_NONE to "No error",
        ERROR_CONNECTION_TIMEOUT to "Connection timeout",
        ERROR_CONNECTION_FAILED to "Connection failed",
        ERROR_CONNECTION_LOST to "Connection lost",
        ERROR_WRITE_FAILED to "Write failed",
        ERROR_BLUETOOTH_NOT_AVAILABLE to "Bluetooth not available",
        ERROR_BLUETOOTH_NOT_ENABLED to "Bluetooth not enabled",
        ERROR_DEVICE_NOT_FOUND to "Device not found",
        ERROR_INVALID_ADDRESS to "Invalid device address",
        ERROR_BLE_NOT_SUPPORTED to "BLE not supported",
        ERROR_BLE_SERVICES_NOT_FOUND to "BLE services not found"
    )
    
    // Connection Timeout in milliseconds
    const val CONNECTION_TIMEOUT = 10000L // 10 seconds
    
    // BLE UUIDs (common ones)
    val UUID_BLE_GENERIC_ACCESS = UUID.fromString("00001800-0000-1000-8000-00805F9B34FB")
    val UUID_BLE_DEVICE_NAME = UUID.fromString("00002A00-0000-1000-8000-00805F9B34FB")
    val UUID_BLE_GENERIC_ATTRIBUTE = UUID.fromString("00001801-0000-1000-8000-00805F9B34FB")
    val UUID_BLE_SERVICE_CHANGED = UUID.fromString("00002A05-0000-1000-8000-00805F9B34FB")
}
```

### 2. Updated BluetoothService.kt (with Timeout, Message Queuing, and BLE Support)
```kotlin
import android.app.Service
import android.bluetooth.*
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.util.Log
import java.io.IOException
import java.util.*
import java.util.concurrent.BlockingQueue
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicBoolean

class BluetoothService : Service() {
    private val binder = LocalBinder()
    private var bluetoothAdapter: BluetoothAdapter? = null
    private var socket: BluetoothSocket? = null
    private var device: BluetoothDevice? = null
    private var connectedThread: ConnectedThread? = null
    private var bleGatt: BluetoothGatt? = null
    private var connectionState = STATE_NONE
    private val isConnecting = AtomicBoolean(false)
    private val messageQueue: BlockingQueue<ByteArray> = LinkedBlockingQueue()
    private var bleCallback: BluetoothGattCallback? = null

    companion object {
        const val TAG = "BluetoothService"
        const val STATE_NONE = 0
        const val STATE_CONNECTING = 1
        const val STATE_CONNECTED = 2
        const val STATE_BLE_CONNECTED = 3
        val UUID_SPP = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
    }

    inner class LocalBinder : Binder() {
        fun getService(): BluetoothService = this@BluetoothService
    }

    override fun onBind(intent: Intent): IBinder = binder

    override fun onCreate() {
        super.onCreate()
        initialize()
    }

    fun initialize(): Boolean {
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        if (bluetoothAdapter == null) {
            broadcastUpdate(ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_BLUETOOTH_NOT_AVAILABLE)
            return false
        }
        if (!bluetoothAdapter!!.isEnabled) {
            broadcastUpdate(ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_BLUETOOTH_NOT_ENABLED)
            return false
        }
        return true
    }

    fun connect(device: BluetoothDevice, useBLE: Boolean = false): Boolean {
        if (connectionState == STATE_CONNECTED || connectionState == STATE_BLE_CONNECTED) {
            disconnect()
        }

        if (isConnecting.get()) {
            closeSocket()
            closeBLEConnection()
        }

        isConnecting.set(true)
        this.device = device
        connectionState = STATE_CONNECTING

        if (useBLE) {
            connectBLE(device)
        } else {
            connectSPP(device)
        }

        return true
    }

    private fun connectSPP(device: BluetoothDevice) {
        Thread {
            try {
                val tmpSocket = device.createRfcommSocketToServiceRecord(UUID_SPP)
                bluetoothAdapter?.cancelDiscovery()
                tmpSocket.connect()
                
                socket = tmpSocket
                connectedThread = ConnectedThread(socket)
                connectedThread?.start()
                connectionState = STATE_CONNECTED
                isConnecting.set(false)
                broadcastUpdate(ACTION_CONNECTED, device.address)
            } catch (e: IOException) {
                connectionState = STATE_NONE
                isConnecting.set(false)
                broadcastUpdate(ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_CONNECTION_FAILED)
                closeSocket()
            }
        }.start()

        // Timeout thread
        Thread {
            Thread.sleep(BluetoothConstants.CONNECTION_TIMEOUT)
            if (isConnecting.get()) {
                isConnecting.set(false)
                closeSocket()
                connectionState = STATE_NONE
                broadcastUpdate(ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_CONNECTION_TIMEOUT)
            }
        }.start()
    }

    private fun connectBLE(device: BluetoothDevice) {
        if (!packageManager.hasSystemFeature(PackageManager.FEATURE_BLUETOOTH_LE)) {
            broadcastUpdate(ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_BLE_NOT_SUPPORTED)
            isConnecting.set(false)
            return
        }

        bleCallback = object : BluetoothGattCallback() {
            override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
                if (newState == BluetoothProfile.STATE_CONNECTED) {
                    connectionState = STATE_BLE_CONNECTED
                    isConnecting.set(false)
                    broadcastUpdate(ACTION_CONNECTED, device.address)
                    gatt.discoverServices()
                } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                    connectionState = STATE_NONE
                    broadcastUpdate(ACTION_DISCONNECTED)
                    closeBLEConnection()
                }
            }

            override fun onServicesDiscovered(gatt: BluetoothGatt, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    broadcastUpdate(ACTION_BLE_SERVICES_DISCOVERED)
                } else {
                    broadcastUpdate(ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_BLE_SERVICES_NOT_FOUND)
                }
            }

            override fun onCharacteristicChanged(
                gatt: BluetoothGatt,
                characteristic: BluetoothGattCharacteristic
            ) {
                val data = characteristic.value
                broadcastUpdate(ACTION_BLE_DATA_AVAILABLE, data)
            }
        }

        bleGatt = device.connectGatt(this, false, bleCallback)
        
        // Timeout thread
        Thread {
            Thread.sleep(BluetoothConstants.CONNECTION_TIMEOUT)
            if (isConnecting.get()) {
                isConnecting.set(false)
                closeBLEConnection()
                connectionState = STATE_NONE
                broadcastUpdate(ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_CONNECTION_TIMEOUT)
            }
        }.start()
    }

    fun write(bytes: ByteArray) {
        when (connectionState) {
            STATE_CONNECTED -> {
                messageQueue.add(bytes)
                connectedThread?.notifyNewMessage()
            }
            STATE_BLE_CONNECTED -> {
                // For BLE, we need to write to a specific characteristic
                // This is a simplified implementation - in practice you'd identify the correct characteristic
                bleGatt?.services?.forEach { service ->
                    service.characteristics.forEach { characteristic ->
                        if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE != 0) {
                            characteristic.value = bytes
                            bleGatt?.writeCharacteristic(characteristic)
                        }
                    }
                }
            }
        }
    }

    fun disconnect() {
        when (connectionState) {
            STATE_CONNECTED -> {
                connectedThread?.cancel()
                closeSocket()
            }
            STATE_BLE_CONNECTED -> {
                closeBLEConnection()
            }
        }
        connectionState = STATE_NONE
        broadcastUpdate(ACTION_DISCONNECTED)
    }

    private fun closeSocket() {
        try {
            socket?.close()
        } catch (e: IOException) {
            Log.e(TAG, "Socket close error", e)
        }
    }

    private fun closeBLEConnection() {
        bleGatt?.disconnect()
        bleGatt?.close()
        bleGatt = null
    }

    private inner class ConnectedThread(private val socket: BluetoothSocket?) {
        private val inputStream = socket?.inputStream
        private val outputStream = socket?.outputStream
        private val buffer = ByteArray(1024)
        private val shouldContinue = AtomicBoolean(true)
        private val hasMessage = Any() // For wait/notify
        private val readThread = Thread {
            while (shouldContinue.get()) {
                try {
                    val bytes = inputStream?.read(buffer) ?: break
                    val message = String(buffer, 0, bytes)
                    broadcastUpdate(ACTION_MESSAGE_RECEIVED, message)
                } catch (e: IOException) {
                    if (shouldContinue.get()) {
                        connectionState = STATE_NONE
                        broadcastUpdate(ACTION_CONNECTION_LOST, BluetoothConstants.ERROR_CONNECTION_LOST)
                    }
                    break
                }
            }
        }
        private val writeThread = Thread {
            while (shouldContinue.get()) {
                try {
                    val bytes = if (messageQueue.isEmpty()) {
                        synchronized(hasMessage) {
                            hasMessage.wait()
                        }
                        messageQueue.peek()
                    } else {
                        messageQueue.peek()
                    }

                    if (bytes != null) {
                        outputStream?.write(bytes)
                        messageQueue.remove()
                        broadcastUpdate(ACTION_MESSAGE_SENT)
                    }
                } catch (e: InterruptedException) {
                    if (shouldContinue.get()) {
                        Log.e(TAG, "Write thread interrupted", e)
                    }
                } catch (e: IOException) {
                    broadcastUpdate(ACTION_WRITE_FAILED, BluetoothConstants.ERROR_WRITE_FAILED)
                }
            }
        }

        fun start() {
            readThread.start()
            writeThread.start()
        }

        fun notifyNewMessage() {
            synchronized(hasMessage) {
                hasMessage.notify()
            }
        }

        fun cancel() {
            shouldContinue.set(false)
            readThread.interrupt()
            writeThread.interrupt()
            try {
                socket?.close()
            } catch (e: IOException) {
                Log.e(TAG, "Thread cancel error", e)
            }
        }
    }

    private fun broadcastUpdate(action: String, errorCode: Int = BluetoothConstants.ERROR_NONE, data: Any = "") {
        val intent = Intent(action).apply {
            putExtra("ERROR_CODE", errorCode)
            putExtra("ERROR_MESSAGE", BluetoothConstants.ERROR_MESSAGES[errorCode] ?: "Unknown error")
            when (data) {
                is String -> putExtra("MESSAGE", data)
                is ByteArray -> putExtra("DATA", data)
                is BluetoothDevice -> putExtra("DEVICE", data)
            }
            putExtra("DEVICE_ADDRESS", device?.address)
        }
        sendBroadcast(intent)
    }
}
```

### 3. Updated BluetoothBroadcastReceiver.kt (with BLE Support)
```kotlin
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.le.ScanResult
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build

class BluetoothBroadcastReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            BluetoothAdapter.ACTION_STATE_CHANGED -> {
                val state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)
                handleBluetoothStateChange(state)
            }
            
            BluetoothDevice.ACTION_FOUND -> {
                val device: BluetoothDevice? = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                device?.let { handleDeviceFound(it) }
            }
            
            BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                handleDiscoveryFinished()
            }
            
            BluetoothDevice.ACTION_ACL_CONNECTED -> {
                val device: BluetoothDevice? = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                device?.let { handleDeviceConnected(it) }
            }
            
            BluetoothDevice.ACTION_ACL_DISCONNECTED -> {
                val device: BluetoothDevice? = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                device?.let { handleDeviceDisconnected(it) }
            }
            
            // BLE specific actions
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                BluetoothLeScanner.ACTION_SCAN_FAILED -> {
                    handleBLEScanFailed()
                }
            }
        }
        
        // Handle BLE scan results if available
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            when (intent.action) {
                BluetoothLeScanner.ACTION_SCAN_FAILED -> {
                    handleBLEScanFailed()
                }
            }
        }
        
        // Check for BLE scan results
        if (intent.action == "android.bluetooth.le.ScanResult") {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                val scanResult: ScanResult? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    intent.getParcelableExtra("scanResult", ScanResult::class.java)
                } else {
                    @Suppress("DEPRECATION")
                    intent.getParcelableExtra("scanResult")
                }
                scanResult?.let { handleBLEDeviceFound(it) }
            }
        }
    }

    private fun handleBluetoothStateChange(state: Int) {
        when (state) {
            BluetoothAdapter.STATE_ON -> {
                broadcastUpdate(ACTION_BLUETOOTH_ENABLED)
            }
            BluetoothAdapter.STATE_OFF -> {
                broadcastUpdate(ACTION_BLUETOOTH_DISABLED)
            }
        }
    }

    private fun handleDeviceFound(device: BluetoothDevice) {
        broadcastUpdate(ACTION_DEVICE_DISCOVERED).apply {
            putExtra("DEVICE", device)
        }
    }

    private fun handleDiscoveryFinished() {
        broadcastUpdate(ACTION_DISCOVERY_FINISHED)
    }

    private fun handleDeviceConnected(device: BluetoothDevice) {
        broadcastUpdate(ACTION_DEVICE_CONNECTED).apply {
            putExtra("DEVICE", device)
        }
    }

    private fun handleDeviceDisconnected(device: BluetoothDevice) {
        broadcastUpdate(ACTION_DEVICE_DISCONNECTED).apply {
            putExtra("DEVICE", device)
        }
    }

    private fun handleBLEScanFailed() {
        broadcastUpdate(ACTION_CONNECTION_FAILED, BluetoothConstants.ERROR_BLE_NOT_SUPPORTED)
    }

    private fun handleBLEDeviceFound(scanResult: ScanResult) {
        broadcastUpdate(ACTION_BLE_DEVICE_DISCOVERED).apply {
            putExtra("DEVICE", scanResult.device)
            putExtra("RSSI", scanResult.rssi)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                putExtra("MANUFACTURER_DATA", scanResult.scanRecord?.manufacturerSpecificData)
            }
        }
    }

    private fun broadcastUpdate(action: String): Intent {
        return Intent(action).apply {
            // Add any common extras here
        }
    }
}
```

### 4. BLE Scanner Helper Class
```kotlin
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.le.BluetoothLeScanner
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanFilter
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.ParcelUuid
import java.util.*

class BLEScanner(private val context: Context) {
    private var bluetoothAdapter: BluetoothAdapter? = null
    private var bluetoothLeScanner: BluetoothLeScanner? = null
    private var scanning = false
    private var scanCallback: ScanCallback? = null

    init {
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        if (bluetoothAdapter != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            bluetoothLeScanner = bluetoothAdapter!!.bluetoothLeScanner
        }
    }

    fun startScan(serviceUUID: UUID? = null) {
        if (scanning) return
        
        if (bluetoothLeScanner == null) {
            sendError(BluetoothConstants.ERROR_BLE_NOT_SUPPORTED)
            return
        }

        scanCallback = object : ScanCallback() {
            override fun onScanResult(callbackType: Int, result: ScanResult) {
                super.onScanResult(callbackType, result)
                val intent = Intent(BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED).apply {
                    putExtra("DEVICE", result.device)
                    putExtra("RSSI", result.rssi)
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        putExtra("MANUFACTURER_DATA", result.scanRecord?.manufacturerSpecificData)
                    }
                }
                context.sendBroadcast(intent)
            }

            override fun onBatchScanResults(results: MutableList<ScanResult>) {
                super.onBatchScanResults(results)
                results.forEach { result ->
                    val intent = Intent(BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED).apply {
                        putExtra("DEVICE", result.device)
                        putExtra("RSSI", result.rssi)
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            putExtra("MANUFACTURER_DATA", result.scanRecord?.manufacturerSpecificData)
                        }
                    }
                    context.sendBroadcast(intent)
                }
            }

            override fun onScanFailed(errorCode: Int) {
                super.onScanFailed(errorCode)
                sendError(BluetoothConstants.ERROR_BLE_NOT_SUPPORTED)
            }
        }

        val scanFilters = mutableListOf<ScanFilter>()
        serviceUUID?.let {
            scanFilters.add(ScanFilter.Builder().setServiceUuid(ParcelUuid(it)).build())
        }

        val scanSettings = ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_POWER)
            .build()

        bluetoothLeScanner?.startScan(scanFilters, scanSettings, scanCallback)
        scanning = true
    }

    fun stopScan() {
        if (!scanning) return
        
        bluetoothLeScanner?.stopScan(scanCallback)
        scanCallback = null
        scanning = false
        
        val intent = Intent(BluetoothConstants.ACTION_DISCOVERY_FINISHED)
        context.sendBroadcast(intent)
    }

    private fun sendError(errorCode: Int) {
        val intent = Intent(BluetoothConstants.ACTION_CONNECTION_FAILED).apply {
            putExtra("ERROR_CODE", errorCode)
            putExtra("ERROR_MESSAGE", BluetoothConstants.ERROR_MESSAGES[errorCode] ?: "Unknown error")
        }
        context.sendBroadcast(intent)
    }
}
```

### 5. Usage Example (Updated for BLE)
```kotlin
class BluetoothActivity : AppCompatActivity() {
    private lateinit var bluetoothService: BluetoothService
    private lateinit var bleScanner: BLEScanner
    private var isBound = false
    private val receiver = BluetoothBroadcastReceiver()
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {
            val binder = service as BluetoothService.LocalBinder
            bluetoothService = binder.getService()
            isBound = true
        }

        override fun onServiceDisconnected(arg0: ComponentName) {
            isBound = false
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Initialize service
        Intent(this, BluetoothService::class.java).also { intent ->
            bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
        }
        
        // Initialize BLE scanner
        bleScanner = BLEScanner(this)
        
        // Register receiver
        val filter = IntentFilter().apply {
            addAction(BluetoothConstants.ACTION_CONNECTED)
            addAction(BluetoothConstants.ACTION_DISCONNECTED)
            addAction(BluetoothConstants.ACTION_MESSAGE_RECEIVED)
            addAction(BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED)
            addAction(BluetoothConstants.ACTION_BLE_SERVICES_DISCOVERED)
            addAction(BluetoothConstants.ACTION_BLE_DATA_AVAILABLE)
            // Add other actions as needed
        }
        registerReceiver(receiver, filter)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (isBound) {
            unbindService(serviceConnection)
            isBound = false
        }
        unregisterReceiver(receiver)
        bleScanner.stopScan()
    }

    fun connectToDevice(device: BluetoothDevice, useBLE: Boolean = false) {
        if (isBound) {
            bluetoothService.connect(device, useBLE)
        }
    }

    fun sendData(data: String) {
        if (isBound) {
            bluetoothService.write(data.toByteArray())
        }
    }
    
    fun startBLEScan(serviceUUID: UUID? = null) {
        bleScanner.startScan(serviceUUID)
    }
    
    fun stopBLEScan() {
        bleScanner.stopScan()
    }
}
```

### 6. Updated AndroidManifest.xml (with BLE Permissions)
```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.bluetooth">

    <!-- Bluetooth permissions -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    
    <!-- For Android 12+ -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    
    <!-- BLE feature requirement -->
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="false" />

    <application>
        <service
            android:name=".BluetoothService"
            android:enabled="true"
            android:exported="false" />
            
        <receiver
            android:name=".BluetoothBroadcastReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.bluetooth.adapter.action.STATE_CHANGED" />
                <action android:name="android.bluetooth.device.action.FOUND" />
                <action android:name="android.bluetooth.adapter.action.DISCOVERY_FINISHED" />
                <action android:name="android.bluetooth.device.action.ACL_CONNECTED" />
                <action android:name="android.bluetooth.device.action.ACL_DISCONNECTED" />
                <action android:name="android.bluetooth.le.ScanResult" />
            </intent-filter>
        </receiver>
    </application>
</manifest>
```

### Key Enhancements:

1. **Error Handling**:
   - Added comprehensive error codes and messages
   - Better error reporting through broadcasts

2. **Connection Timeout**:
   - Implemented connection timeout mechanism
   - Prevents indefinite waiting during connection attempts

3. **Message Queuing**:
   - Added a queue for outgoing messages
   - Ensures messages are sent in order and without loss
   - Uses separate threads for reading and writing

4. **BLE Support**:
   - Added full BLE support with GATT operations
   - BLE scanning with filters
   - Service discovery and characteristic operations
   - Separate BLE scanner helper class

5. **Thread Management**:
   - Improved thread handling with AtomicBoolean
   - Better thread interruption and cleanup

6. **Documentation**:
   - Added detailed comments
   - Clear separation between SPP and BLE operations

This enhanced implementation provides a professional-grade solution for both classic Bluetooth (SPP) and Bluetooth Low Energy (BLE) communication with embedded devices. It handles all essential use cases including connection management, data transfer, error handling, and system event monitoring.