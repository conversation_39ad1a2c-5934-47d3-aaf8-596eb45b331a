

# Jetpack Compose Bluetooth Integration

I'll create a comprehensive, Compose-friendly Bluetooth solution that leverages all the features we built earlier. This implementation will provide a clean, reactive API for Compose UIs with proper state management and lifecycle handling.

## 1. Bluetooth State Management

First, let's define our Bluetooth state classes to represent different UI states:

```kotlin
// BluetoothState.kt
sealed class BluetoothState {
    object Idle : BluetoothState()
    object Connecting : BluetoothState()
    data class Connected(val device: BluetoothDevice) : BluetoothState()
    object Disconnecting : BluetoothState()
    data class Error(val message: String, val code: Int) : BluetoothState()
}

// DeviceType.kt
enum class DeviceType {
    CLASSIC, BLE, UNKNOWN
}

// BluetoothDeviceWrapper.kt
data class BluetoothDeviceWrapper(
    val device: BluetoothDevice,
    val name: String,
    val address: String,
    val type: DeviceType,
    val rssi: Int? = null,
    val isBonded: Boolean = false
)
```

## 2. Bluetooth ViewModel

```kotlin
// BluetoothViewModel.kt
class BluetoothViewModel(application: Application) : AndroidViewModel(application) {
    private val context = application.applicationContext
    private val bluetoothService = BluetoothService()
    private val bleScanner = BLEScanner(context)
    
    private val _bluetoothState = mutableStateOf<BluetoothState>(BluetoothState.Idle)
    val bluetoothState: State<BluetoothState> = _bluetoothState
    
    private val _discoveredDevices = mutableStateListOf<BluetoothDeviceWrapper>()
    val discoveredDevices: List<BluetoothDeviceWrapper> = _discoveredDevices
    
    private val _messages = mutableStateListOf<String>()
    val messages: List<String> = _messages
    
    private val _isScanning = mutableStateOf(false)
    val isScanning: State<Boolean> = _isScanning
    
    private val _isBluetoothEnabled = mutableStateOf(false)
    val isBluetoothEnabled: State<Boolean> = _isBluetoothEnabled
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {
            val binder = service as BluetoothService.LocalBinder
            bluetoothService.initialize()
        }
        
        override fun onServiceDisconnected(arg0: ComponentName) {
            // Handle disconnection
        }
    }
    
    private val bluetoothReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                BluetoothConstants.ACTION_CONNECTED -> {
                    val deviceAddress = intent.getStringExtra("DEVICE_ADDRESS")
                    val device = _discoveredDevices.find { it.address == deviceAddress }?.device
                    device?.let {
                        _bluetoothState.value = BluetoothState.Connected(it)
                    }
                }
                BluetoothConstants.ACTION_DISCONNECTED -> {
                    _bluetoothState.value = BluetoothState.Idle
                }
                BluetoothConstants.ACTION_CONNECTION_FAILED -> {
                    val errorCode = intent.getIntExtra("ERROR_CODE", BluetoothConstants.ERROR_NONE)
                    val errorMessage = intent.getStringExtra("ERROR_MESSAGE") ?: "Unknown error"
                    _bluetoothState.value = BluetoothState.Error(errorMessage, errorCode)
                }
                BluetoothConstants.ACTION_MESSAGE_RECEIVED -> {
                    val message = intent.getStringExtra("MESSAGE") ?: ""
                    _messages.add(message)
                }
                BluetoothConstants.ACTION_DEVICE_DISCOVERED, 
                BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED -> {
                    val device: BluetoothDevice? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra("DEVICE", BluetoothDevice::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra("DEVICE")
                    }
                    val rssi = intent.getIntExtra("RSSI", 0)
                    val isBLE = intent.action == BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED
                    device?.let {
                        val wrapper = BluetoothDeviceWrapper(
                            device = it,
                            name = it.name ?: "Unknown",
                            address = it.address,
                            type = if (isBLE) DeviceType.BLE else DeviceType.CLASSIC,
                            rssi = if (isBLE) rssi else null,
                            isBonded = it.bondState == BluetoothDevice.BOND_BONDED
                        )
                        if (!_discoveredDevices.any { it.address == wrapper.address }) {
                            _discoveredDevices.add(wrapper)
                        }
                    }
                }
                BluetoothConstants.ACTION_DISCOVERY_FINISHED -> {
                    _isScanning.value = false
                }
                BluetoothConstants.ACTION_BLUETOOTH_ENABLED -> {
                    _isBluetoothEnabled.value = true
                }
                BluetoothConstants.ACTION_BLUETOOTH_DISABLED -> {
                    _isBluetoothEnabled.value = false
                }
            }
        }
    }
    
    init {
        // Register receiver
        val filter = IntentFilter().apply {
            addAction(BluetoothConstants.ACTION_CONNECTED)
            addAction(BluetoothConstants.ACTION_DISCONNECTED)
            addAction(BluetoothConstants.ACTION_CONNECTION_FAILED)
            addAction(BluetoothConstants.ACTION_MESSAGE_RECEIVED)
            addAction(BluetoothConstants.ACTION_DEVICE_DISCOVERED)
            addAction(BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED)
            addAction(BluetoothConstants.ACTION_DISCOVERY_FINISHED)
            addAction(BluetoothConstants.ACTION_BLUETOOTH_ENABLED)
            addAction(BluetoothConstants.ACTION_BLUETOOTH_DISABLED)
        }
        context.registerReceiver(bluetoothReceiver, filter)
        
        // Bind to service
        Intent(context, BluetoothService::class.java).also { intent ->
            context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        context.unregisterReceiver(bluetoothReceiver)
        context.unbindService(serviceConnection)
        bleScanner.stopScan()
    }
    
    fun startScan() {
        _discoveredDevices.clear()
        _isScanning.value = true
        bleScanner.startScan()
    }
    
    fun stopScan() {
        bleScanner.stopScan()
        _isScanning.value = false
    }
    
    fun connect(device: BluetoothDeviceWrapper) {
        _bluetoothState.value = BluetoothState.Connecting
        bluetoothService.connect(device.device, device.type == DeviceType.BLE)
    }
    
    fun disconnect() {
        _bluetoothState.value = BluetoothState.Disconnecting
        bluetoothService.disconnect()
    }
    
    fun sendMessage(message: String) {
        bluetoothService.write(message.toByteArray())
    }
    
    fun clearMessages() {
        _messages.clear()
    }
    
    fun enableBluetooth() {
        val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
        enableBtIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(enableBtIntent)
    }
}
```

## 3. Composable UI Components

```kotlin
// BluetoothComponents.kt
@Composable
fun BluetoothScreen(viewModel: BluetoothViewModel = viewModel()) {
    val bluetoothState by viewModel.bluetoothState
    val discoveredDevices by viewModel.discoveredDevices
    val messages by viewModel.messages
    val isScanning by viewModel.isScanning
    val isBluetoothEnabled by viewModel.isBluetoothEnabled
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Bluetooth status bar
        BluetoothStatusBar(
            isEnabled = isBluetoothEnabled,
            state = bluetoothState,
            onEnable = { viewModel.enableBluetooth() }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Device list and controls
        when (bluetoothState) {
            is BluetoothState.Connected -> {
                ConnectedDeviceScreen(
                    device = (bluetoothState as BluetoothState.Connected).device,
                    messages = messages,
                    onSendMessage = { viewModel.sendMessage(it) },
                    onDisconnect = { viewModel.disconnect() },
                    onClearMessages = { viewModel.clearMessages() }
                )
            }
            else -> {
                DeviceListScreen(
                    devices = discoveredDevices,
                    isScanning = isScanning,
                    onScanStart = { viewModel.startScan() },
                    onScanStop = { viewModel.stopScan() },
                    onDeviceClick = { viewModel.connect(it) }
                )
            }
        }
    }
}

@Composable
fun BluetoothStatusBar(
    isEnabled: Boolean,
    state: BluetoothState,
    onEnable: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Bluetooth Status",
                    style = MaterialTheme.typography.h6
                )
                
                Icon(
                    imageVector = if (isEnabled) Icons.Default.Bluetooth else Icons.Default.BluetoothDisabled,
                    contentDescription = if (isEnabled) "Bluetooth Enabled" else "Bluetooth Disabled",
                    tint = if (isEnabled) MaterialTheme.colors.primary else MaterialTheme.colors.error
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            when (state) {
                is BluetoothState.Error -> {
                    Text(
                        text = (state as BluetoothState.Error).message,
                        color = MaterialTheme.colors.error
                    )
                }
                is BluetoothState.Connecting -> {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Connecting...")
                    }
                }
                is BluetoothState.Connected -> {
                    Text(
                        text = "Connected to ${(state as BluetoothState.Connected).device.name}",
                        color = MaterialTheme.colors.primary
                    )
                }
                else -> {
                    Text(
                        text = if (isEnabled) "Ready to connect" else "Bluetooth disabled",
                        color = if (isEnabled) MaterialTheme.colors.onSurface else MaterialTheme.colors.error
                    )
                }
            }
            
            if (!isEnabled) {
                Spacer(modifier = Modifier.height(8.dp))
                Button(
                    onClick = onEnable,
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Text("Enable Bluetooth")
                }
            }
        }
    }
}

@Composable
fun DeviceListScreen(
    devices: List<BluetoothDeviceWrapper>,
    isScanning: Boolean,
    onScanStart: () -> Unit,
    onScanStop: () -> Unit,
    onDeviceClick: (BluetoothDeviceWrapper) -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Available Devices",
                style = MaterialTheme.typography.h6
            )
            
            Row {
                if (isScanning) {
                    Button(onClick = onScanStop) {
                        Text("Stop Scan")
                    }
                } else {
                    Button(onClick = onScanStart) {
                        Text("Start Scan")
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        if (devices.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = if (isScanning) "Scanning for devices..." else "No devices found",
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                items(devers) { device ->
                    DeviceItem(
                        device = device,
                        onClick = { onDeviceClick(device) }
                    )
                }
            }
        }
    }
}

@Composable
fun DeviceItem(
    device: BluetoothDeviceWrapper,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable { onClick() },
        elevation = 2.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = device.name,
                    style = MaterialTheme.typography.subtitle1
                )
                Text(
                    text = device.address,
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                )
            }
            
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        imageVector = when (device.type) {
                            DeviceType.CLASSIC -> Icons.Default.Bluetooth
                            DeviceType.BLE -> Icons.Default.BluetoothConnected
                            DeviceType.UNKNOWN -> Icons.Default.BluetoothDisabled
                        },
                        contentDescription = "Device Type",
                        tint = MaterialTheme.colors.primary
                    )
                    
                    if (device.isBonded) {
                        Spacer(modifier = Modifier.width(4.dp))
                        Icon(
                            imageVector = Icons.Default.Lock,
                            contentDescription = "Paired",
                            tint = MaterialTheme.colors.secondary
                        )
                    }
                }
                
                device.rssi?.let {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "$it dBm",
                        style = MaterialTheme.typography.caption
                    )
                }
            }
        }
    }
}

@Composable
fun ConnectedDeviceScreen(
    device: BluetoothDevice,
    messages: List<String>,
    onSendMessage: (String) -> Unit,
    onDisconnect: () -> Unit,
    onClearMessages: () -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Connected to ${device.name}",
                style = MaterialTheme.typography.h6
            )
            
            Button(onClick = onDisconnect) {
                Text("Disconnect")
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Messages area
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            elevation = 2.dp
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Messages",
                        style = MaterialTheme.typography.subtitle1
                    )
                    
                    if (messages.isNotEmpty()) {
                        TextButton(onClick = onClearMessages) {
                            Text("Clear")
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                if (messages.isEmpty()) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "No messages yet",
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                        )
                    }
                } else {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        reverseLayout = true
                    ) {
                        items(messages) { message ->
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f)
                            ) {
                                Text(
                                    text = message,
                                    modifier = Modifier.padding(12.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Message input
        MessageInput(onSendMessage = onSendMessage)
    }
}

@Composable
fun MessageInput(
    onSendMessage: (String) -> Unit
) {
    var message by remember { mutableStateOf("") }
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        OutlinedTextField(
            value = message,
            onValueChange = { message = it },
            label = { Text("Message") },
            modifier = Modifier.weight(1f),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
            keyboardActions = KeyboardActions(
                onSend = {
                    if (message.isNotBlank()) {
                        onSendMessage(message)
                        message = ""
                    }
                }
            )
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        IconButton(
            onClick = {
                if (message.isNotBlank()) {
                    onSendMessage(message)
                    message = ""
                }
            },
            enabled = message.isNotBlank()
        ) {
            Icon(
                imageVector = Icons.Default.Send,
                contentDescription = "Send Message"
            )
        }
    }
}
```

## 4. Permission Handling

```kotlin
// BluetoothPermissionHandler.kt
@Composable
fun BluetoothPermissionHandler(
    onPermissionsGranted: () -> Unit,
    onPermissionsDenied: () -> Unit
) {
    val activity = LocalContext.current as Activity
    
    val bluetoothPermissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        listOf(
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.BLUETOOTH_ADVERTISE
        )
    } else {
        listOf(
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )
    }
    
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        if (permissions.all { it.value }) {
            onPermissionsGranted()
        } else {
            onPermissionsDenied()
        }
    }
    
    LaunchedEffect(Unit) {
        val hasAllPermissions = bluetoothPermissions.all {
            ContextCompat.checkSelfPermission(activity, it) == PackageManager.PERMISSION_GRANTED
        }
        
        if (hasAllPermissions) {
            onPermissionsGranted()
        } else {
            permissionLauncher.launch(bluetoothPermissions.toTypedArray())
        }
    }
}
```

## 5. Main Screen with Permission Handling

```kotlin
// MainActivity.kt
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            AppTheme {
                BluetoothApp()
            }
        }
    }
}

@Composable
fun AppTheme(
    content: @Composable () -> Unit
) {
    MaterialTheme(
        colors = lightColors(
            primary = Color(0xFF0066CC),
            primaryVariant = Color(0xFF004C99),
            secondary = Color(0xFF03DAC6),
            secondaryVariant = Color(0xFF018786),
            background = Color.White,
            surface = Color.White,
            error = Color(0xFFB00020),
            onPrimary = Color.White,
            onSecondary = Color.Black,
            onBackground = Color.Black,
            onSurface = Color.Black,
            onError = Color.White
        ),
        typography = Typography(
            h1 = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Bold,
                fontSize = 30.sp
            ),
            h2 = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Bold,
                fontSize = 24.sp
            ),
            h3 = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Bold,
                fontSize = 20.sp
            ),
            h4 = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Bold,
                fontSize = 18.sp
            ),
            h5 = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp
            ),
            h6 = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp
            ),
            subtitle1 = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Normal,
                fontSize = 16.sp
            ),
            subtitle2 = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Normal,
                fontSize = 14.sp
            ),
            body1 = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Normal,
                fontSize = 16.sp
            ),
            body2 = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Normal,
                fontSize = 14.sp
            ),
            button = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp
            ),
            caption = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Normal,
                fontSize = 12.sp
            ),
            overline = TextStyle(
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Normal,
                fontSize = 12.sp
            )
        ),
        shapes = Shapes(
            small = RoundedCornerShape(4.dp),
            medium = RoundedCornerShape(4.dp),
            large = RoundedCornerShape(0.dp)
        ),
        content = content
    )
}

@Composable
fun BluetoothApp() {
    var hasPermissions by remember { mutableStateOf(false) }
    var showPermissionDenied by remember { mutableStateOf(false) }
    
    if (hasPermissions) {
        BluetoothScreen()
    } else {
        BluetoothPermissionHandler(
            onPermissionsGranted = { hasPermissions = true },
            onPermissionsDenied = { showPermissionDenied = true }
        )
        
        if (showPermissionDenied) {
            PermissionDeniedScreen(
                onRetry = { showPermissionDenied = false },
                onOpenSettings = {
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.fromParts("package", "com.example.bluetooth", null)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    LocalContext.current.startActivity(intent)
                }
            )
        }
    }
}

@Composable
fun PermissionDeniedScreen(
    onRetry: () -> Unit,
    onOpenSettings: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Warning,
            contentDescription = "Warning",
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colors.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "Bluetooth Permissions Required",
            style = MaterialTheme.typography.h6
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "This app needs Bluetooth permissions to connect with devices. Please grant the necessary permissions.",
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Row {
            Button(onClick = onRetry) {
                Text("Retry")
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            OutlinedButton(onClick = onOpenSettings) {
                Text("Open Settings")
            }
        }
    }
}
```

## 6. AndroidManifest.xml

```xml
<!-- AndroidManifest.xml -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.bluetooth">

    <!-- Bluetooth permissions -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    
    <!-- For Android 12+ -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    
    <!-- BLE feature requirement -->
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="false" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.BluetoothApp">
        
        <service
            android:name=".BluetoothService"
            android:enabled="true"
            android:exported="false" />
            
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.BluetoothApp">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>
```

## Key Features of This Implementation

1. **Reactive UI with Compose**:
   - Uses Compose's state management for reactive UI updates
   - All UI components automatically update when Bluetooth state changes
   - Clean separation between UI and business logic

2. **Comprehensive State Management**:
   - Handles all Bluetooth states (idle, connecting, connected, error)
   - Manages discovered devices list
   - Tracks messages and connection status

3. **Modern Permission Handling**:
   - Properly handles both legacy and new Bluetooth permissions
   - Provides clear UI for permission requests and denials
   - Works across all Android versions

4. **Professional UI Components**:
   - Clean, Material Design-compliant interface
   - Responsive layouts for different screen sizes
   - Clear visual indicators for device types and connection status

5. **Complete Bluetooth Support**:
   - Supports both classic Bluetooth (SPP) and BLE devices
   - Handles device discovery, connection, and messaging
   - Includes error handling and user feedback

6. **Lifecycle-Aware**:
   - Properly handles service binding and unbinding
   - Manages BroadcastReceiver registration
   - Cleans up resources when not needed

This implementation provides a complete, production-ready solution for Bluetooth communication in Jetpack Compose applications. It's designed to be easily integrated into any app and provides all the essential features for professional Bluetooth development.